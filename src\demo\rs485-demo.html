<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RS485 Configuration Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 30px;
        }
        .demo-section h3 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .config-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .config-item:last-child {
            border-bottom: none;
        }
        .config-label {
            font-weight: bold;
            color: #495057;
        }
        .config-value {
            color: #007bff;
        }
        .slave-config {
            background: #e9ecef;
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .indoor-groups {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 5px;
        }
        .indoor-group {
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            background: #e3f2fd;
            margin: 5px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
        .feature-list li::before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 RS485 Configuration System</h1>
            <p>Complete implementation based on C# WinForms reference</p>
        </div>

        <div class="demo-section">
            <h3>📋 Implementation Features</h3>
            <ul class="feature-list">
                <li>Complete RS485 configuration dialog with tabbed interface</li>
                <li>Support for 2 RS485 configurations (RS485-1 and RS485-2)</li>
                <li>Interface configuration: Baudrate, Parity, Stop Bit</li>
                <li>Host configuration: Type, Board ID, Number of Slaves</li>
                <li>Dynamic slave configuration with up to 10 slaves</li>
                <li>Indoor group configuration with up to 16 indoor units per slave</li>
                <li>Database integration with JSON storage</li>
                <li>Validation and utility functions</li>
                <li>Unit type support detection</li>
                <li>Serialization/Deserialization compatible with C# format</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>🎛️ Sample RS485 Configuration</h3>
            <div class="config-display">
                <h4>RS485-1 Configuration</h4>
                <div class="config-item">
                    <span class="config-label">Baudrate:</span>
                    <span class="config-value">9600</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Parity:</span>
                    <span class="config-value">None</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Stop Bit:</span>
                    <span class="config-value">1</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Type:</span>
                    <span class="config-value">RS485_MASTER_DAIKIN</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Board ID:</span>
                    <span class="config-value">1</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Number of Slaves:</span>
                    <span class="config-value">2</span>
                </div>

                <div class="slave-config">
                    <h5>Slave 1 Configuration</h5>
                    <div class="config-item">
                        <span class="config-label">Slave ID:</span>
                        <span class="config-value">1</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Slave Group:</span>
                        <span class="config-value">0</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Number of Indoors:</span>
                        <span class="config-value">4</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Indoor Groups:</span>
                        <div class="indoor-groups">
                            <span class="indoor-group">In1: 1</span>
                            <span class="indoor-group">In2: 2</span>
                            <span class="indoor-group">In3: 3</span>
                            <span class="indoor-group">In4: 4</span>
                        </div>
                    </div>
                </div>

                <div class="slave-config">
                    <h5>Slave 2 Configuration</h5>
                    <div class="config-item">
                        <span class="config-label">Slave ID:</span>
                        <span class="config-value">2</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Slave Group:</span>
                        <span class="config-value">1</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Number of Indoors:</span>
                        <span class="config-value">2</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Indoor Groups:</span>
                        <div class="indoor-groups">
                            <span class="indoor-group">In1: 5</span>
                            <span class="indoor-group">In2: 6</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🔧 Supported Unit Types</h3>
            <div class="config-display">
                <p><strong>RS485 is supported for the following unit types:</strong></p>
                <ul>
                    <li>Room Logic Controller</li>
                    <li>RLC-I16, RLC-I20</li>
                    <li>RCU-32AO</li>
                    <li>RCU-8RL-24AO, RCU-16RL-16AO, RCU-24RL-8AO</li>
                    <li>RCU-11IN-4RL, RCU-21IN-10RL, RCU-30IN-10RL, RCU-21IN-8RL</li>
                    <li>RCU-48IN-16RL, RCU-48IN-16RL-4AO, RCU-48IN-16RL-4AI</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 JSON Data Structure</h3>
            <div class="json-display" id="jsonDisplay">
Loading sample configuration...
            </div>
        </div>

        <div class="demo-section">
            <h3>🚀 How to Use</h3>
            <div class="config-display">
                <ol>
                    <li><strong>Create/Edit Unit:</strong> Open the Unit dialog for a supported unit type</li>
                    <li><strong>Configure RS485:</strong> Click the "Configure RS485" button</li>
                    <li><strong>Set Interface:</strong> Configure baudrate, parity, and stop bit</li>
                    <li><strong>Set Host:</strong> Choose RS485 type, board ID, and number of slaves</li>
                    <li><strong>Configure Slaves:</strong> Set up each slave device and their indoor groups</li>
                    <li><strong>Save:</strong> Configuration is stored in the database as JSON</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // Sample RS485 configuration data
        const sampleConfig = [
            {
                baudrate: 9600,
                parity: 0,
                stop_bit: 0,
                board_id: 1,
                config_type: 10, // RS485_MASTER_DAIKIN
                num_slave_devs: 2,
                reserved: [0, 0, 0, 0, 0],
                slave_cfg: [
                    {
                        slave_id: 1,
                        slave_group: 0,
                        num_indoors: 4,
                        indoor_group: [1, 2, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    },
                    {
                        slave_id: 2,
                        slave_group: 1,
                        num_indoors: 2,
                        indoor_group: [5, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    },
                    // ... 8 more default slave configs
                    ...Array.from({length: 8}, () => ({
                        slave_id: 1,
                        slave_group: 0,
                        num_indoors: 0,
                        indoor_group: Array.from({length: 16}, () => 0)
                    }))
                ]
            },
            {
                baudrate: 19200,
                parity: 1,
                stop_bit: 0,
                board_id: 2,
                config_type: 0, // RS485_NONE
                num_slave_devs: 0,
                reserved: [0, 0, 0, 0, 0],
                slave_cfg: Array.from({length: 10}, () => ({
                    slave_id: 1,
                    slave_group: 0,
                    num_indoors: 0,
                    indoor_group: Array.from({length: 16}, () => 0)
                }))
            }
        ];

        // Display the JSON configuration
        document.getElementById('jsonDisplay').textContent = JSON.stringify(sampleConfig, null, 2);
    </script>
</body>
</html>
