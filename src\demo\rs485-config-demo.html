<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RS485 Configuration Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .config-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .slave-section {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: white;
        }
        .slave-header {
            font-weight: bold;
            margin-bottom: 10px;
            padding: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .slave-content {
            display: none;
            padding: 10px 0;
        }
        .slave-content.open {
            display: block;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .indoor-groups {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .chevron {
            transition: transform 0.2s;
        }
        .chevron.rotated {
            transform: rotate(180deg);
        }
        .highlight {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info-title {
            font-weight: bold;
            color: #0c5460;
            margin-bottom: 8px;
        }
        .info-text {
            color: #0c5460;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>RS485 Configuration Demo</h1>
        
        <div class="info-box">
            <div class="info-title">🔧 New Features Implemented:</div>
            <div class="info-text">
                ✅ <strong>Slave Group & Indoor Group:</strong> Now use combobox to select from aircon devices list<br>
                ✅ <strong>Collapsible Slaves:</strong> Replaced tabs with collapsible sections for better display when many slaves<br>
                ✅ <strong>Aircon Integration:</strong> Options show aircon name and address (e.g., "Living Room AC (1)")
            </div>
        </div>

        <div class="config-section">
            <div class="config-title">RS485-1 Configuration</div>
            
            <!-- Interface Config -->
            <div class="form-group">
                <h3>Interface Config</h3>
                <div class="form-row">
                    <div>
                        <label>Baudrate:</label>
                        <select>
                            <option value="9600">9600</option>
                            <option value="19200">19200</option>
                            <option value="38400">38400</option>
                        </select>
                    </div>
                    <div>
                        <label>Parity:</label>
                        <select>
                            <option value="0">None</option>
                            <option value="1">Odd</option>
                            <option value="2">Even</option>
                        </select>
                    </div>
                    <div>
                        <label>Stop Bit:</label>
                        <select>
                            <option value="0">1 bit</option>
                            <option value="1">2 bits</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Host Config -->
            <div class="form-group">
                <h3>Host Config</h3>
                <div class="form-row">
                    <div>
                        <label>Type:</label>
                        <select id="rs485Type">
                            <option value="10">RS485 Master Daikin</option>
                            <option value="11">RS485 Master Mitsubishi</option>
                            <option value="0">None</option>
                        </select>
                    </div>
                    <div>
                        <label>Board ID:</label>
                        <input type="number" value="1" min="1" max="255">
                    </div>
                    <div>
                        <label>Num Slaves:</label>
                        <select id="numSlaves" onchange="updateSlaves()">
                            <option value="0">0</option>
                            <option value="1">1</option>
                            <option value="2" selected>2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Slave Config -->
            <div class="form-group">
                <h3>Slave Config</h3>
                <div id="slavesContainer">
                    <!-- Slaves will be generated here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample aircon data
        const airconOptions = [
            { value: "0", label: "<Unused>" },
            { value: "1", label: "Living Room AC (1)" },
            { value: "2", label: "Bedroom AC (2)" },
            { value: "3", label: "Kitchen AC (3)" },
            { value: "4", label: "Office AC (4)" },
            { value: "5", label: "Aircon 5" },
            { value: "6", label: "Aircon 6" }
        ];

        function createAirconSelect(selectedValue = "0") {
            const select = document.createElement('select');
            airconOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                if (option.value === selectedValue) {
                    optionElement.selected = true;
                }
                select.appendChild(optionElement);
            });
            return select;
        }

        function toggleSlave(slaveIndex) {
            const content = document.getElementById(`slave-content-${slaveIndex}`);
            const chevron = document.getElementById(`chevron-${slaveIndex}`);
            
            if (content.classList.contains('open')) {
                content.classList.remove('open');
                chevron.classList.remove('rotated');
            } else {
                content.classList.add('open');
                chevron.classList.add('rotated');
            }
        }

        function updateIndoorGroups(slaveIndex) {
            const numIndoors = parseInt(document.getElementById(`num-indoors-${slaveIndex}`).value);
            const container = document.getElementById(`indoor-groups-${slaveIndex}`);
            
            container.innerHTML = '';
            
            for (let i = 0; i < numIndoors; i++) {
                const div = document.createElement('div');
                div.innerHTML = `
                    <label>In${i + 1} Group:</label>
                `;
                const select = createAirconSelect();
                div.appendChild(select);
                container.appendChild(div);
            }
        }

        function updateSlaves() {
            const numSlaves = parseInt(document.getElementById('numSlaves').value);
            const container = document.getElementById('slavesContainer');
            
            container.innerHTML = '';
            
            for (let i = 0; i < numSlaves; i++) {
                const slaveDiv = document.createElement('div');
                slaveDiv.className = 'slave-section';
                slaveDiv.innerHTML = `
                    <div class="slave-header" onclick="toggleSlave(${i})">
                        <span>Slave ${i + 1}</span>
                        <span id="chevron-${i}" class="chevron">▼</span>
                    </div>
                    <div id="slave-content-${i}" class="slave-content">
                        <div class="form-row">
                            <div>
                                <label>Slave ID:</label>
                                <input type="number" value="${i + 1}" min="1" max="255">
                            </div>
                            <div>
                                <label>Slave Group:</label>
                                <div id="slave-group-${i}"></div>
                            </div>
                            <div>
                                <label>Num Indoors:</label>
                                <select id="num-indoors-${i}" onchange="updateIndoorGroups(${i})">
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4" selected>4</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label><strong>Indoor Groups:</strong></label>
                            <div id="indoor-groups-${i}" class="indoor-groups"></div>
                        </div>
                    </div>
                `;
                container.appendChild(slaveDiv);
                
                // Add slave group select
                const slaveGroupContainer = document.getElementById(`slave-group-${i}`);
                slaveGroupContainer.appendChild(createAirconSelect(i === 0 ? "1" : "2"));
                
                // Initialize indoor groups
                updateIndoorGroups(i);
            }
        }

        // Initialize on page load
        updateSlaves();
    </script>
</body>
</html>
